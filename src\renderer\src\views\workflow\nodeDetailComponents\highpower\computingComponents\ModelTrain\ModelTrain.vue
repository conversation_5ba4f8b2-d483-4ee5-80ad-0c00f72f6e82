<template>
  <div class="relative">
    <Card class="p-2">
      <CardHeader>
        <div class="flex flex-col space-y-2 sm:flex-row sm:justify-between sm:space-y-0">
          <CardTitle class="text-xl sm:text-2xl font-bold">机器学习模型训练</CardTitle>
          <!-- 操作按钮 -->
          <div class="flex flex-wrap gap-1">
            <Button
              size="xs"
              class="flex items-center relative"
              :disabled="isTrainTaskProcessing"
              @click="openSubmitDialog"
            >
              <LucideIcon v-if="!isTrainTaskProcessing" name="Check" class="w-4 h-4 mr-1" />
              <span v-if="isTrainTaskProcessing" class="animate-spin mr-1">
                <LucideIcon name="RefreshCw" class="h-4 w-4" />
              </span>
              <span class="whitespace-nowrap">开始训练</span>
            </Button>

            <!-- 导出结果按钮 - 只有当有结果且任务暂停或结束后才显示 -->
            <Button
              v-if="state.trainTask.result"
              size="xs"
              class="dark:text-muted-foreground dark:bg-muted-foreground"
              @click="handleExport"
            >
              <LucideIcon name="FileUp" class="w-4 h-4 mr-1" />
              <span class="hidden sm:inline">导出结果</span>
              <span class="sm:hidden">导出</span>
            </Button>

            <!-- 任务控制下拉菜单 - 只有在有任务运行或暂停时才显示 -->
            <DropdownMenu v-if="taskControlMenuItems.length > 0">
              <DropdownMenuTrigger as-child>
                <Button variant="outline" size="xs" class="p-1">
                  <LucideIcon name="EllipsisVertical" class="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  v-for="item in taskControlMenuItems"
                  :key="item.key"
                  :class="item.hoverColor"
                  @click="item.handler"
                >
                  <LucideIcon :name="item.icon" :class="`w-4 h-4 mr-2 ${item.iconColor}`" />
                  <span :class="`${item.textColor} font-medium`">{{ item.label }}</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <!-- 进度条显示 -->
        <div
          v-if="isTrainTaskProcessing"
          class="mb-4 space-y-2 bg-blue-50 p-3 rounded-lg border border-blue-200"
        >
          <div class="flex items-center justify-between text-sm">
            <div class="flex items-center">
              <span class="animate-spin mr-2">
                <LucideIcon name="RefreshCw" class="h-5 w-5 text-blue-500" />
              </span>
              <span class="font-medium text-blue-700">
                <span class="flex items-center justify-center">
                  模型训练中
                  <span class="inline-block w-3 overflow-hidden animate-ellipsis">...</span>
                </span>
              </span>
            </div>
            <span class="text-blue-600">
              处理中 {{ trainTaskProgress && trainTaskProgress.toFixed(2) }}%
            </span>
          </div>
          <Progress v-model="trainTaskProgress" class="w-full h-2" />
          <div class="flex items-center justify-between cursor-pointer">
            <p class="text-xs text-blue-600 mr-2">请耐心等待，任务完成后将自动更新</p>
          </div>
        </div>

        <!-- 基本参数配置 -->
        <BasicSettings
          v-model:model-params="state.modelResult.modelParams"
          :disabled="isTrainTaskProcessing"
        />

        <!-- 训练参数设置 -->
        <TrainSettings v-model:train-params="state.trainParams" :disabled="isTrainTaskProcessing" />

        <!-- 训练结果 -->
        <TrainResult
          :task-id="state.trainTask.taskId"
          :completed="trainTaskCompleted"
          :server-info="{
            server_id: lastServerInfo.server_id,
            service_name: lastServerInfo.service_name,
          }"
          :total-samples="0"
          :result-data="state.trainTask.result"
          :task-status="trainTaskStatus"
          :task-duration="trainTaskDuration"
        />

        <!-- 数据集划分 -->
        <DatasetSplit
          v-model:dataset-data="state.datasetData"
          :is-processing="isTrainTaskProcessing"
          :is-loading-data="isLoadingBatteryData"
          @use-recommended-dataset="handleUseRecommendedDataset"
          @open-custom-dataset="handleOpenCustomDataset"
          @update-chart-settings="handleChartSettingsUpdate"
        />
      </CardContent>
    </Card>

    <!-- 数据集比例配置对话框 -->
    <DatasetRatioDialog
      v-model:open="showDatasetRatioDialog"
      :total-data-count="getTotalDataCount"
      :initial-ratios="state.datasetRatios"
      @confirm="handleDatasetRatioConfirm"
      @cancel="() => {}"
    />

    <!-- 自定义数据集配置对话框 -->
    <CustomDatasetDialog
      v-model:open="showCustomDatasetDialog"
      :dataset-data="state.datasetData"
      @confirm="handleCustomDatasetConfirm"
      @cancel="() => {}"
      @chart-click="handleChartClick"
      @update-chart-settings="handleChartSettingsUpdate"
    />

    <!-- 提交任务对话框 -->
    <SubmitTaskDialog
      v-model:is-open="showSubmitDialog"
      service-name="eolTrain"
      @submit="handleSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { LucideIcon, SubmitTaskDialog } from '@renderer/components'
import { BasicSettings, DatasetSplit, DatasetRatioDialog } from './components'
import CustomDatasetDialog from './components/CustomDatasetDialog.vue'
import TrainSettings from './components/TrainSettings.vue'
import TrainResult from './components/TrainResult.vue'
import { useModelTrain } from './composables/useModelTrain'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@renderer/components/ui/dropdown-menu'

// 任务控制菜单配置
const taskControlMenuItems = computed(() => {
  const items: Array<{
    key: string
    label: string
    icon: string
    iconColor: string
    textColor: string
    hoverColor: string
    handler: () => Promise<void>
  }> = []

  // 暂停按钮 - 任务运行时显示
  if (['Computing', 'Pending', 'Initializing'].includes(trainTaskStatus.value)) {
    items.push({
      key: 'pause',
      label: '暂停',
      icon: 'CirclePause',
      iconColor: 'text-orange-500',
      textColor: 'text-orange-600',
      hoverColor: 'hover:bg-orange-50',
      handler: handlePause,
    })
  }

  // 恢复按钮 - 任务暂停时显示
  if (trainTaskStatus.value === 'Paused') {
    items.push({
      key: 'resume',
      label: '恢复',
      icon: 'Play',
      iconColor: 'text-green-500',
      textColor: 'text-green-600',
      hoverColor: 'hover:bg-green-50',
      handler: handleResume,
    })
  }

  // 终止按钮 - 任务运行或暂停时显示
  if (['Computing', 'Pending', 'Initializing', 'Paused'].includes(trainTaskStatus.value)) {
    items.push({
      key: 'stop',
      label: '终止',
      icon: 'Ban',
      iconColor: 'text-red-500',
      textColor: 'text-red-600',
      hoverColor: 'hover:bg-red-50',
      handler: handleStop,
    })
  }

  return items
})

// 优化图表点击处理，避免内联函数
const handleChartClick = (item: any) => {
  console.log('图表点击:', item)
}

// 组件属性
const props = defineProps({
  nodeData: {
    type: Object,
    required: true,
  },
})

// 使用组合式函数
const {
  state,
  showDatasetRatioDialog,
  showCustomDatasetDialog,
  showSubmitDialog,
  isLoadingBatteryData,
  trainTaskProgress,
  isTrainTaskProcessing,
  trainTaskStatus,
  trainTaskCompleted,
  trainTaskDuration,
  lastServerInfo,
  getTotalDataCount,
  handleExport,
  handlePause,
  handleStop,
  handleResume,
  handleUseRecommendedDataset,
  handleOpenCustomDataset,
  handleDatasetRatioConfirm,
  handleCustomDatasetConfirm,
  handleChartSettingsUpdate,
  setupWatchers,
  openSubmitDialog,
  handleSubmit,
} = useModelTrain(props)

// 生命周期
onMounted(() => {
  console.log('ModelTrain 组件已挂载')
  setupWatchers()
})
</script>

<style scoped></style>
